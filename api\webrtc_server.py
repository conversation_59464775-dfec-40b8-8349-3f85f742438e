"""
WebRTC Signaling Server for VPN Communication Features

Handles peer connections for:
- Video/voice calls
- Screen sharing
- File transfer
"""

from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json
import asyncio
from typing import Dict

app = FastAPI()

# Enable CORS for WebRTC connections
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Store active connections
connections: Dict[str, WebSocket] = {}

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    await websocket.accept()
    connections[client_id] = websocket
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Route signaling messages to target peer
            if "target" in message:
                target_ws = connections.get(message["target"])
                if target_ws:
                    await target_ws.send_text(json.dumps({
                        "sender": client_id,
                        "type": message["type"],
                        "data": message.get("data")
                    }))
    except WebSocketDisconnect:
        del connections[client_id]
        print(f"Client {client_id} disconnected")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
