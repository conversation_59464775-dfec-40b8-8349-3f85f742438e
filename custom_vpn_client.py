#!/usr/bin/env python3
"""
Custom VPN Client Implementation
A high-performance VPN client built from scratch using Python
Features:
- TUN/TAP interface management
- IP packet tunneling
- High-performance encryption
- Multi-platform support
"""

import socket
import threading
import queue
import time
import logging
import os
import sys
import subprocess
import ipaddress
from pathlib import Path
from typing import Optional, <PERSON>ple
import select
import struct
import json
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import secrets
import argparse

# Platform-specific imports
if sys.platform == "win32":
    import wintun
elif sys.platform.startswith("linux"):
    import fcntl

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vpn_client.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class ClientTunInterface:
    """Client-side TUN interface management"""

    def __init__(self, interface_name: str = "vpn_client"):
        self.interface_name = interface_name
        self.tun_fd = None
        self.running = False
        self.assigned_ip = None
        self.gateway_ip = None
        self.netmask = None

    def create_interface(self, ip_config: dict):
        """Create and configure TUN interface"""
        try:
            self.assigned_ip = ip_config['ip']
            self.gateway_ip = ip_config['gateway']
            self.netmask = ip_config['netmask']

            if sys.platform == "win32":
                self._create_windows_interface()
            elif sys.platform.startswith("linux"):
                self._create_linux_interface()
            else:
                raise NotImplementedError(f"Platform {sys.platform} not supported")

            logging.info(f"Client TUN interface created: {self.assigned_ip}")
            return True

        except Exception as e:
            logging.error(f"Failed to create client TUN interface: {e}")
            return False

    def _create_windows_interface(self):
        """Create Windows TUN interface"""
        try:
            # Use netsh to configure interface
            cmd = f'netsh interface ip set address "{self.interface_name}" static {self.assigned_ip} {self.netmask} {self.gateway_ip}'
            subprocess.run(cmd, shell=True, check=True)

        except Exception as e:
            logging.error(f"Windows TUN interface creation failed: {e}")
            raise

    def _create_linux_interface(self):
        """Create Linux TUN interface"""
        try:
            import fcntl

            # Create TUN interface
            TUNSETIFF = 0x400454ca
            IFF_TUN = 0x0001
            IFF_NO_PI = 0x1000

            self.tun_fd = os.open("/dev/net/tun", os.O_RDWR)
            ifr = struct.pack('16sH', self.interface_name.encode(), IFF_TUN | IFF_NO_PI)
            fcntl.ioctl(self.tun_fd, TUNSETIFF, ifr)

            # Configure interface
            subprocess.run([
                'ip', 'addr', 'add', f'{self.assigned_ip}/24', 'dev', self.interface_name
            ], check=True)
            subprocess.run(['ip', 'link', 'set', self.interface_name, 'up'], check=True)

            # Add default route through VPN
            subprocess.run([
                'ip', 'route', 'add', 'default', 'via', self.gateway_ip, 'dev', self.interface_name
            ], check=True)

        except Exception as e:
            logging.error(f"Linux TUN interface creation failed: {e}")
            raise

    def read_packet(self) -> Optional[bytes]:
        """Read packet from TUN interface"""
        try:
            if self.tun_fd:
                return os.read(self.tun_fd, 1500)  # MTU size
        except Exception as e:
            logging.error(f"Error reading from TUN interface: {e}")
        return None

    def write_packet(self, packet: bytes):
        """Write packet to TUN interface"""
        try:
            if self.tun_fd:
                os.write(self.tun_fd, packet)
        except Exception as e:
            logging.error(f"Error writing to TUN interface: {e}")

    def close(self):
        """Close TUN interface"""
        if self.tun_fd:
            os.close(self.tun_fd)
            self.tun_fd = None

class VPNClient:
    def __init__(self, server_host: str, server_port: int):
        self.server_host = server_host
        self.server_port = server_port
        self.socket = None
        self.connected = False
        self.authenticated = False
        self.session_key = None
        self.server_public_key = None
        self.buffer = bytearray()
        self.packet_queue = queue.Queue()
        self.buffer_size = 65536  # 64KB buffer for high performance
        self.keepalive_interval = 30  # seconds
        self.last_activity = time.time()

        # VPN Infrastructure
        self.tun_interface = ClientTunInterface()
        self.assigned_ip = None
        self.server_ip = None

        # Performance monitoring
        self.stats = {
            'bytes_sent': 0,
            'bytes_received': 0,
            'packets_sent': 0,
            'packets_received': 0,
            'tunnel_packets': 0,
            'start_time': time.time()
        }
    
    def connect(self):
        """Connect to the VPN server"""
        try:
            # Load server public key
            self._load_server_public_key()
            
            # Create socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.server_host, self.server_port))
            self.socket.setblocking(False)
            
            self.connected = True
            logging.info(f"Connected to VPN server at {self.server_host}:{self.server_port}")
            
            # Start worker threads
            self._start_workers()
            
            # Send authentication request
            self._send_auth_request()
            
            # Main client loop
            while self.connected:
                try:
                    readable, _, _ = select.select([self.socket], [], [], 1)
                    if self.socket in readable:
                        data = self.socket.recv(self.buffer_size)
                        if not data:
                            self.disconnect()
                            break
                        
                        self.buffer.extend(data)
                        self._process_buffer()
                        
                except Exception as e:
                    logging.error(f"Error in main loop: {e}")
                    self.disconnect()
                    break
            
        except Exception as e:
            logging.error(f"Failed to connect to server: {e}")
            self.disconnect()
    
    def _load_server_public_key(self):
        """Load server public key"""
        try:
            with open('keys/server_public.pem', 'rb') as f:
                self.server_public_key = serialization.load_pem_public_key(
                    f.read(),
                    backend=default_backend()
                )
        except Exception as e:
            logging.error(f"Failed to load server public key: {e}")
            raise
    
    def _start_workers(self):
        """Start worker threads"""
        # Packet processing thread
        threading.Thread(target=self._process_packets, daemon=True).start()

        # TUN interface packet handler
        threading.Thread(target=self._handle_tun_packets, daemon=True).start()

        # Keepalive thread
        threading.Thread(target=self._send_keepalives, daemon=True).start()

        # Stats monitoring thread
        threading.Thread(target=self._monitor_stats, daemon=True).start()

    def _handle_tun_packets(self):
        """Handle packets from TUN interface"""
        while self.connected:
            try:
                packet = self.tun_interface.read_packet()
                if packet and self.authenticated:
                    # Send packet through tunnel
                    tunnel_data = struct.pack('!I', 1) + packet  # Type 1 = IP packet
                    self._send_packet(tunnel_data)
                    self.stats['tunnel_packets'] += 1

            except Exception as e:
                logging.error(f"Error handling TUN packets: {e}")
                time.sleep(0.1)
    
    def _process_packets(self):
        """Process packets in the queue"""
        while self.connected:
            try:
                packet = self.packet_queue.get(timeout=1)
                if packet:
                    self._handle_packet(packet)
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Error processing packet: {e}")
    
    def _process_buffer(self):
        """Process data in the buffer"""
        while len(self.buffer) >= 4:  # Minimum packet size
            try:
                # Get packet length
                length = struct.unpack('!I', self.buffer[:4])[0]
                if len(self.buffer) < length + 4:
                    break
                
                # Extract packet
                packet_data = self.buffer[4:length+4]
                self.buffer = self.buffer[length+4:]
                
                # Process packet
                self._handle_packet(packet_data)
                
            except Exception as e:
                logging.error(f"Error processing buffer: {e}")
                self.disconnect()
                break
    
    def _handle_packet(self, packet_data: bytes):
        """Handle a packet from the server"""
        try:
            if not self.authenticated:
                self._handle_auth_response(packet_data)
            else:
                self._handle_data_packet(packet_data)
            
            self.last_activity = time.time()
            self.stats['bytes_received'] += len(packet_data)
            self.stats['packets_received'] += 1
            
        except Exception as e:
            logging.error(f"Error handling packet: {e}")
    
    def _send_auth_request(self):
        """Send authentication request to server"""
        try:
            # Create auth data
            auth_data = {
                'client_id': secrets.token_hex(8),
                'timestamp': time.time()
            }
            
            # Encrypt with server public key
            encrypted = self.server_public_key.encrypt(
                json.dumps(auth_data).encode(),
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            # Send auth request
            self._send_packet(encrypted)
            
        except Exception as e:
            logging.error(f"Error sending auth request: {e}")
            self.disconnect()
    
    def _handle_auth_response(self, packet_data: bytes):
        """Handle authentication response from server"""
        try:
            # Parse response
            response = json.loads(packet_data)

            if response['success']:
                self.authenticated = True
                self.session_key = bytes.fromhex(response['session_key'])
                self.assigned_ip = response['assigned_ip']
                self.server_ip = response['server_ip']

                # Create TUN interface with assigned IP
                ip_config = {
                    'ip': self.assigned_ip,
                    'gateway': self.server_ip,
                    'netmask': response['netmask']
                }

                if self.tun_interface.create_interface(ip_config):
                    logging.info(f"VPN tunnel established - IP: {self.assigned_ip}")
                else:
                    logging.error("Failed to create TUN interface")
                    self.disconnect()
                    return

            else:
                logging.error("Authentication failed")
                self.disconnect()

        except Exception as e:
            logging.error(f"Error handling auth response: {e}")
            self.disconnect()
    
    def _handle_data_packet(self, packet_data: bytes):
        """Handle data packet from server"""
        try:
            # Decrypt with session key
            iv = packet_data[:16]
            cipher = Cipher(
                algorithms.AES(self.session_key),
                modes.CBC(iv),
                backend=default_backend()
            )
            decryptor = cipher.decryptor()
            decrypted = decryptor.update(packet_data[16:]) + decryptor.finalize()

            # Process decrypted data
            if decrypted == b'KEEPALIVE':
                logging.debug("Received keepalive")
            elif len(decrypted) >= 4:
                # Parse packet type
                packet_type = struct.unpack('!I', decrypted[:4])[0]
                payload = decrypted[4:]

                if packet_type == 1:  # IP packet
                    # Forward to TUN interface
                    self.tun_interface.write_packet(payload)
                    self.stats['tunnel_packets'] += 1
                elif packet_type == 2:  # Control packet
                    self._handle_control_packet(payload)

        except Exception as e:
            logging.error(f"Error handling data packet: {e}")

    def _handle_control_packet(self, payload: bytes):
        """Handle control packet from server"""
        try:
            control_data = json.loads(payload.decode())
            if control_data.get('type') == 'ip_config':
                logging.info(f"Received IP config: {control_data}")
        except Exception as e:
            logging.error(f"Error handling control packet: {e}")
    
    def _send_keepalives(self):
        """Send keepalive packets to server"""
        while self.connected:
            try:
                if time.time() - self.last_activity > self.keepalive_interval * 2:
                    self.disconnect()
                    break
                
                self._send_packet(b'KEEPALIVE')
                time.sleep(self.keepalive_interval)
                
            except Exception as e:
                logging.error(f"Error sending keepalive: {e}")
                self.disconnect()
                break
    
    def _monitor_stats(self):
        """Monitor and log client statistics"""
        while self.connected:
            try:
                uptime = time.time() - self.stats['start_time']
                logging.info(
                    f"Stats - Uptime: {uptime:.1f}s | "
                    f"Bytes Sent: {self.stats['bytes_sent']} | "
                    f"Bytes Received: {self.stats['bytes_received']} | "
                    f"Packets Sent: {self.stats['packets_sent']} | "
                    f"Packets Received: {self.stats['packets_received']}"
                )
                time.sleep(60)  # Log stats every minute
            except Exception as e:
                logging.error(f"Error monitoring stats: {e}")
    
    def _send_packet(self, data: bytes):
        """Send a packet to the server"""
        try:
            # Encrypt with session key if authenticated
            if self.authenticated:
                iv = secrets.token_bytes(16)
                cipher = Cipher(
                    algorithms.AES(self.session_key),
                    modes.CBC(iv),
                    backend=default_backend()
                )
                encryptor = cipher.encryptor()
                encrypted = encryptor.update(data) + encryptor.finalize()
                data = iv + encrypted
            
            # Add packet length
            packet = struct.pack('!I', len(data)) + data
            
            # Send packet
            self.socket.sendall(packet)
            self.stats['bytes_sent'] += len(packet)
            self.stats['packets_sent'] += 1
            
        except Exception as e:
            logging.error(f"Error sending packet: {e}")
            self.disconnect()
    
    def disconnect(self):
        """Disconnect from the server"""
        self.connected = False

        # Close TUN interface
        self.tun_interface.close()

        if self.socket:
            self.socket.close()

        logging.info("Disconnected from VPN server")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Custom VPN Client')
    parser.add_argument('--host', default='localhost', help='VPN server host')
    parser.add_argument('--port', type=int, default=5000, help='VPN server port')
    args = parser.parse_args()
    
    try:
        client = VPNClient(args.host, args.port)
        client.connect()
    except KeyboardInterrupt:
        logging.info("Client stopped by user")
    except Exception as e:
        logging.error(f"Client error: {e}")
    finally:
        if 'client' in locals():
            client.disconnect()

if __name__ == "__main__":
    main() 