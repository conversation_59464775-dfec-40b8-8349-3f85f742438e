#!/usr/bin/env python3
"""
Custom VPN Server Implementation
A high-performance VPN server built from scratch using Python
Features:
- TUN/TAP interface management
- IP address pool management
- Packet routing and forwarding
- High-performance encryption
- Multi-platform support
"""

import socket
import threading
import queue
import time
import logging
import os
import sys
import subprocess
import ipaddress
from pathlib import Path
from typing import Dict, Optional, Tuple, List
import select
import struct
import json
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import secrets

# Platform-specific imports
if sys.platform == "win32":
    import wintun
elif sys.platform.startswith("linux"):
    import fcntl

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vpn_server.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class TunInterface:
    """TUN interface management for VPN tunneling"""

    def __init__(self, interface_name: str = "vpn0", ip_address: str = "********", netmask: str = "*************"):
        self.interface_name = interface_name
        self.ip_address = ip_address
        self.netmask = netmask
        self.tun_fd = None
        self.running = False

    def create_interface(self):
        """Create TUN interface"""
        try:
            if sys.platform == "win32":
                self._create_windows_interface()
            elif sys.platform.startswith("linux"):
                self._create_linux_interface()
            else:
                raise NotImplementedError(f"Platform {sys.platform} not supported")

            logging.info(f"TUN interface {self.interface_name} created successfully")
            return True

        except Exception as e:
            logging.error(f"Failed to create TUN interface: {e}")
            return False

    def _create_windows_interface(self):
        """Create Windows TUN interface using TAP driver"""
        try:
            # Use netsh to create TAP interface
            cmd = f'netsh interface ip set address "{self.interface_name}" static {self.ip_address} {self.netmask}'
            subprocess.run(cmd, shell=True, check=True)

        except Exception as e:
            logging.error(f"Windows TUN interface creation failed: {e}")
            raise

    def _create_linux_interface(self):
        """Create Linux TUN interface"""
        try:
            import fcntl

            # Create TUN interface
            TUNSETIFF = 0x400454ca
            IFF_TUN = 0x0001
            IFF_NO_PI = 0x1000

            self.tun_fd = os.open("/dev/net/tun", os.O_RDWR)
            ifr = struct.pack('16sH', self.interface_name.encode(), IFF_TUN | IFF_NO_PI)
            fcntl.ioctl(self.tun_fd, TUNSETIFF, ifr)

            # Configure interface
            subprocess.run([
                'ip', 'addr', 'add', f'{self.ip_address}/24', 'dev', self.interface_name
            ], check=True)
            subprocess.run(['ip', 'link', 'set', self.interface_name, 'up'], check=True)

        except Exception as e:
            logging.error(f"Linux TUN interface creation failed: {e}")
            raise

    def read_packet(self) -> Optional[bytes]:
        """Read packet from TUN interface"""
        try:
            if self.tun_fd:
                return os.read(self.tun_fd, 1500)  # MTU size
        except Exception as e:
            logging.error(f"Error reading from TUN interface: {e}")
        return None

    def write_packet(self, packet: bytes):
        """Write packet to TUN interface"""
        try:
            if self.tun_fd:
                os.write(self.tun_fd, packet)
        except Exception as e:
            logging.error(f"Error writing to TUN interface: {e}")

    def close(self):
        """Close TUN interface"""
        if self.tun_fd:
            os.close(self.tun_fd)
            self.tun_fd = None

class IPPool:
    """IP address pool management for VPN clients"""

    def __init__(self, network: str = "********/24", start_ip: str = "********0"):
        self.network = ipaddress.IPv4Network(network)
        self.start_ip = ipaddress.IPv4Address(start_ip)
        self.allocated_ips: Dict[str, str] = {}
        self.available_ips = list(self.network.hosts())[9:]  # Skip first 10 IPs

    def allocate_ip(self, client_id: str) -> Optional[str]:
        """Allocate IP address to client"""
        if client_id in self.allocated_ips:
            return self.allocated_ips[client_id]

        if self.available_ips:
            ip = str(self.available_ips.pop(0))
            self.allocated_ips[client_id] = ip
            logging.info(f"Allocated IP {ip} to client {client_id}")
            return ip

        logging.error("No available IP addresses in pool")
        return None

    def release_ip(self, client_id: str):
        """Release IP address from client"""
        if client_id in self.allocated_ips:
            ip = self.allocated_ips.pop(client_id)
            self.available_ips.append(ipaddress.IPv4Address(ip))
            logging.info(f"Released IP {ip} from client {client_id}")

class PacketRouter:
    """Packet routing and forwarding engine"""

    def __init__(self, tun_interface: TunInterface):
        self.tun_interface = tun_interface
        self.routing_table: Dict[str, str] = {}  # IP -> client_id mapping

    def add_route(self, ip_address: str, client_id: str):
        """Add route for client IP"""
        self.routing_table[ip_address] = client_id
        logging.debug(f"Added route: {ip_address} -> {client_id}")

    def remove_route(self, ip_address: str):
        """Remove route for IP"""
        if ip_address in self.routing_table:
            del self.routing_table[ip_address]
            logging.debug(f"Removed route for {ip_address}")

    def route_packet(self, packet: bytes) -> Optional[str]:
        """Route packet to appropriate client"""
        try:
            # Parse IP header to get destination
            if len(packet) < 20:
                return None

            # Extract destination IP from IP header
            dest_ip = socket.inet_ntoa(packet[16:20])

            # Find client for this IP
            return self.routing_table.get(dest_ip)

        except Exception as e:
            logging.error(f"Error routing packet: {e}")
            return None

class VPNServer:
    def __init__(self, host: str = '0.0.0.0', port: int = 5000):
        self.host = host
        self.port = port
        self.server_socket = None
        self.clients: Dict[str, 'VPNClient'] = {}
        self.running = False
        self.packet_queue = queue.Queue()
        self.max_clients = 1000
        self.buffer_size = 65536  # 64KB buffer for high performance
        self.keepalive_interval = 30  # seconds

        # VPN Infrastructure
        self.tun_interface = TunInterface()
        self.ip_pool = IPPool()
        self.packet_router = PacketRouter(self.tun_interface)

        # Generate server keys
        self._generate_keys()

        # Performance monitoring
        self.stats = {
            'bytes_sent': 0,
            'bytes_received': 0,
            'packets_sent': 0,
            'packets_received': 0,
            'active_connections': 0,
            'start_time': time.time(),
            'packets_routed': 0,
            'tunnel_bytes': 0
        }
    
    def _generate_keys(self):
        """Generate RSA key pair for the server"""
        self.private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=default_backend()
        )
        self.public_key = self.private_key.public_key()
        
        # Save keys
        keys_dir = Path('keys')
        keys_dir.mkdir(exist_ok=True)
        
        # Save private key
        with open(keys_dir / 'server_private.pem', 'wb') as f:
            f.write(self.private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        # Save public key
        with open(keys_dir / 'server_public.pem', 'wb') as f:
            f.write(self.public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            ))
    
    def start(self):
        """Start the VPN server"""
        try:
            # Create TUN interface first
            if not self.tun_interface.create_interface():
                raise Exception("Failed to create TUN interface")

            # Start socket server
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(self.max_clients)
            self.server_socket.setblocking(False)

            self.running = True
            logging.info(f"VPN Server started on {self.host}:{self.port}")
            logging.info(f"TUN interface: {self.tun_interface.interface_name}")

            # Start worker threads
            self._start_workers()

            # Main server loop
            while self.running:
                try:
                    readable, _, _ = select.select([self.server_socket], [], [], 1)
                    if self.server_socket in readable:
                        client_socket, address = self.server_socket.accept()
                        self._handle_new_connection(client_socket, address)
                except Exception as e:
                    logging.error(f"Error in main loop: {e}")
                    continue

        except Exception as e:
            logging.error(f"Failed to start server: {e}")
            self.stop()
    
    def _start_workers(self):
        """Start worker threads for packet processing"""
        # Packet processing thread
        threading.Thread(target=self._process_packets, daemon=True).start()

        # TUN interface packet handler
        threading.Thread(target=self._handle_tun_packets, daemon=True).start()

        # Keepalive thread
        threading.Thread(target=self._send_keepalives, daemon=True).start()

        # Stats monitoring thread
        threading.Thread(target=self._monitor_stats, daemon=True).start()

    def _handle_tun_packets(self):
        """Handle packets from TUN interface"""
        while self.running:
            try:
                packet = self.tun_interface.read_packet()
                if packet:
                    # Route packet to appropriate client
                    client_id = self.packet_router.route_packet(packet)
                    if client_id and client_id in self.clients:
                        self.clients[client_id].send_tunnel_packet(packet)
                        self.stats['packets_routed'] += 1
                        self.stats['tunnel_bytes'] += len(packet)

            except Exception as e:
                logging.error(f"Error handling TUN packets: {e}")
                time.sleep(0.1)
    
    def _handle_new_connection(self, client_socket: socket.socket, address: Tuple[str, int]):
        """Handle new client connection"""
        try:
            client_socket.setblocking(False)
            client = VPNClient(client_socket, address, self)

            # Allocate IP address for client
            client_ip = self.ip_pool.allocate_ip(client.id)
            if not client_ip:
                logging.error(f"No IP available for client {client.id}")
                client_socket.close()
                return

            client.assigned_ip = client_ip
            self.packet_router.add_route(client_ip, client.id)

            self.clients[client.id] = client
            self.stats['active_connections'] = len(self.clients)
            logging.info(f"New client connected: {client.id} from {address}, assigned IP: {client_ip}")

        except Exception as e:
            logging.error(f"Error handling new connection: {e}")
            client_socket.close()
    
    def _process_packets(self):
        """Process packets in the queue"""
        while self.running:
            try:
                packet = self.packet_queue.get(timeout=1)
                if packet:
                    self._handle_packet(packet)
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Error processing packet: {e}")
    
    def _handle_packet(self, packet: dict):
        """Handle a packet from a client"""
        try:
            client_id = packet['client_id']
            if client_id in self.clients:
                self.clients[client_id].handle_packet(packet)
        except Exception as e:
            logging.error(f"Error handling packet: {e}")
    
    def _send_keepalives(self):
        """Send keepalive packets to clients"""
        while self.running:
            try:
                for client in list(self.clients.values()):
                    client.send_keepalive()
                time.sleep(self.keepalive_interval)
            except Exception as e:
                logging.error(f"Error sending keepalives: {e}")
    
    def _monitor_stats(self):
        """Monitor and log server statistics"""
        while self.running:
            try:
                uptime = time.time() - self.stats['start_time']
                logging.info(
                    f"Stats - Uptime: {uptime:.1f}s | "
                    f"Active Clients: {self.stats['active_connections']} | "
                    f"Bytes Sent: {self.stats['bytes_sent']} | "
                    f"Bytes Received: {self.stats['bytes_received']} | "
                    f"Packets Sent: {self.stats['packets_sent']} | "
                    f"Packets Received: {self.stats['packets_received']}"
                )
                time.sleep(60)  # Log stats every minute
            except Exception as e:
                logging.error(f"Error monitoring stats: {e}")
    
    def stop(self):
        """Stop the VPN server"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
        for client in list(self.clients.values()):
            client.disconnect()

        # Close TUN interface
        self.tun_interface.close()

        logging.info("VPN Server stopped")

class VPNClient:
    def __init__(self, socket: socket.socket, address: Tuple[str, int], server: VPNServer):
        self.socket = socket
        self.address = address
        self.server = server
        self.id = secrets.token_hex(8)
        self.authenticated = False
        self.session_key = None
        self.assigned_ip = None
        self.last_activity = time.time()
        self.buffer = bytearray()

        # Start client handler thread
        threading.Thread(target=self._handle_client, daemon=True).start()
    
    def _handle_client(self):
        """Handle client connection"""
        while self.server.running:
            try:
                readable, _, _ = select.select([self.socket], [], [], 1)
                if self.socket in readable:
                    data = self.socket.recv(self.server.buffer_size)
                    if not data:
                        self.disconnect()
                        break
                    
                    self.buffer.extend(data)
                    self._process_buffer()
                    
            except Exception as e:
                logging.error(f"Error handling client {self.id}: {e}")
                self.disconnect()
                break
    
    def _process_buffer(self):
        """Process data in the buffer"""
        while len(self.buffer) >= 4:  # Minimum packet size
            try:
                # Get packet length
                length = struct.unpack('!I', self.buffer[:4])[0]
                if len(self.buffer) < length + 4:
                    break
                
                # Extract packet
                packet_data = self.buffer[4:length+4]
                self.buffer = self.buffer[length+4:]
                
                # Process packet
                self._handle_packet(packet_data)
                
            except Exception as e:
                logging.error(f"Error processing buffer for client {self.id}: {e}")
                self.disconnect()
                break
    
    def _handle_packet(self, packet_data: bytes):
        """Handle a packet from the client"""
        try:
            if not self.authenticated:
                self._handle_auth_packet(packet_data)
            else:
                self._handle_data_packet(packet_data)
            
            self.last_activity = time.time()
            self.server.stats['bytes_received'] += len(packet_data)
            self.server.stats['packets_received'] += 1
            
        except Exception as e:
            logging.error(f"Error handling packet from client {self.id}: {e}")
    
    def _handle_auth_packet(self, packet_data: bytes):
        """Handle authentication packet"""
        try:
            # Decrypt with server private key
            decrypted = self.server.private_key.decrypt(
                packet_data,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            # Parse auth data
            auth_data = json.loads(decrypted)
            if self._verify_auth(auth_data):
                self.authenticated = True
                self.session_key = secrets.token_bytes(32)  # Generate session key
                self._send_auth_response(True)
            else:
                self._send_auth_response(False)
                self.disconnect()
                
        except Exception as e:
            logging.error(f"Error handling auth packet from client {self.id}: {e}")
            self.disconnect()
    
    def _verify_auth(self, auth_data: dict) -> bool:
        """Verify client authentication"""
        # TODO: Implement proper authentication
        return True  # For now, accept all clients
    
    def _send_auth_response(self, success: bool):
        """Send authentication response to client"""
        try:
            response = {
                'success': success,
                'session_key': self.session_key.hex() if success else None,
                'assigned_ip': self.assigned_ip if success else None,
                'server_ip': '********',
                'netmask': '*************'
            }
            self._send_packet(json.dumps(response).encode())
        except Exception as e:
            logging.error(f"Error sending auth response to client {self.id}: {e}")
    
    def _handle_data_packet(self, packet_data: bytes):
        """Handle data packet"""
        try:
            # Decrypt with session key
            iv = packet_data[:16]
            cipher = Cipher(
                algorithms.AES(self.session_key),
                modes.CBC(iv),
                backend=default_backend()
            )
            decryptor = cipher.decryptor()
            decrypted = decryptor.update(packet_data[16:]) + decryptor.finalize()

            # Check packet type
            if len(decrypted) >= 4:
                packet_type = struct.unpack('!I', decrypted[:4])[0]
                payload = decrypted[4:]

                if packet_type == 1:  # IP packet
                    # Forward to TUN interface
                    self.server.tun_interface.write_packet(payload)
                    self.server.stats['tunnel_bytes'] += len(payload)
                elif packet_type == 2:  # Control packet
                    self._handle_control_packet(payload)

        except Exception as e:
            logging.error(f"Error handling data packet from client {self.id}: {e}")

    def _handle_control_packet(self, payload: bytes):
        """Handle control packet"""
        try:
            control_data = json.loads(payload.decode())
            if control_data.get('type') == 'ip_request':
                # Send IP configuration to client
                self._send_ip_config()
        except Exception as e:
            logging.error(f"Error handling control packet: {e}")

    def _send_ip_config(self):
        """Send IP configuration to client"""
        try:
            config = {
                'type': 'ip_config',
                'ip': self.assigned_ip,
                'netmask': '*************',
                'gateway': '********',
                'dns': ['*******', '*******']
            }

            # Send as control packet
            control_data = struct.pack('!I', 2) + json.dumps(config).encode()
            self._send_packet(control_data)

        except Exception as e:
            logging.error(f"Error sending IP config: {e}")

    def send_tunnel_packet(self, packet: bytes):
        """Send IP packet through tunnel to client"""
        try:
            # Encapsulate as tunnel packet
            tunnel_data = struct.pack('!I', 1) + packet
            self._send_packet(tunnel_data)

        except Exception as e:
            logging.error(f"Error sending tunnel packet: {e}")
    
    def send_keepalive(self):
        """Send keepalive packet to client"""
        try:
            if time.time() - self.last_activity > self.server.keepalive_interval * 2:
                self.disconnect()
                return
            
            self._send_packet(b'KEEPALIVE')
        except Exception as e:
            logging.error(f"Error sending keepalive to client {self.id}: {e}")
            self.disconnect()
    
    def _send_packet(self, data: bytes):
        """Send a packet to the client"""
        try:
            # Encrypt with session key if authenticated
            if self.authenticated:
                iv = secrets.token_bytes(16)
                cipher = Cipher(
                    algorithms.AES(self.session_key),
                    modes.CBC(iv),
                    backend=default_backend()
                )
                encryptor = cipher.encryptor()
                encrypted = encryptor.update(data) + encryptor.finalize()
                data = iv + encrypted
            
            # Add packet length
            packet = struct.pack('!I', len(data)) + data
            
            # Send packet
            self.socket.sendall(packet)
            self.server.stats['bytes_sent'] += len(packet)
            self.server.stats['packets_sent'] += 1
            
        except Exception as e:
            logging.error(f"Error sending packet to client {self.id}: {e}")
            self.disconnect()
    
    def disconnect(self):
        """Disconnect the client"""
        try:
            # Release IP address
            if self.assigned_ip:
                self.server.ip_pool.release_ip(self.id)
                self.server.packet_router.remove_route(self.assigned_ip)

            # Remove from clients
            if self.id in self.server.clients:
                del self.server.clients[self.id]
                self.server.stats['active_connections'] = len(self.server.clients)

            self.socket.close()
            logging.info(f"Client disconnected: {self.id}")

        except Exception as e:
            logging.error(f"Error disconnecting client {self.id}: {e}")

def main():
    """Main function"""
    try:
        server = VPNServer()
        server.start()
    except KeyboardInterrupt:
        logging.info("Server stopped by user")
    except Exception as e:
        logging.error(f"Server error: {e}")
    finally:
        if 'server' in locals():
            server.stop()

if __name__ == "__main__":
    main() 