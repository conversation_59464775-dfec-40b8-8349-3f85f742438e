from fastapi import Request
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.backends import default_backend

def generate_ephemeral_key_pair():
    return ec.generate_private_key(ec.SECP384R1(), default_backend())

EPHEMERAL_KEYS = {}

async def key_middleware(request: Request, call_next):
    if request.url.path.startswith("/api/"):
        client_key = generate_ephemeral_key_pair()
        EPHEMERAL_KEYS[request.session.get("id")] = client_key
        request.state.client_key = client_key
    return await call_next(request)
