#!/usr/bin/env python3
"""
Simple Custom VPN Test - Works without TUN/TAP for basic testing
"""

import socket
import threading
import time
import logging
import json
import struct
import secrets
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SimpleVPNServer:
    """Simplified VPN server for testing without TUN/TAP"""
    
    def __init__(self, host='127.0.0.1', port=5000):
        self.host = host
        self.port = port
        self.running = False
        self.clients = {}
        self.stats = {'connections': 0, 'bytes_sent': 0, 'bytes_received': 0}
        
        # Generate keys
        self.private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=default_backend()
        )
        self.public_key = self.private_key.public_key()
        
        # Save public key for client
        with open('simple_server_public.pem', 'wb') as f:
            f.write(self.public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            ))
    
    def start(self):
        """Start the simple VPN server"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(10)
            
            self.running = True
            logging.info(f"🚀 Simple VPN Server started on {self.host}:{self.port}")
            
            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    client_id = secrets.token_hex(4)
                    
                    logging.info(f"📱 Client connected: {client_id} from {address}")
                    
                    # Handle client in thread
                    threading.Thread(
                        target=self._handle_client,
                        args=(client_socket, client_id, address),
                        daemon=True
                    ).start()
                    
                except Exception as e:
                    if self.running:
                        logging.error(f"Error accepting connection: {e}")
                        
        except Exception as e:
            logging.error(f"Server error: {e}")
        finally:
            self.stop()
    
    def _handle_client(self, client_socket, client_id, address):
        """Handle client connection"""
        try:
            self.clients[client_id] = {
                'socket': client_socket,
                'address': address,
                'authenticated': False,
                'session_key': None,
                'assigned_ip': f"10.8.0.{len(self.clients) + 10}"
            }
            
            self.stats['connections'] += 1
            
            while self.running:
                try:
                    # Receive data
                    data = client_socket.recv(4096)
                    if not data:
                        break
                    
                    self.stats['bytes_received'] += len(data)
                    
                    # Handle authentication
                    if not self.clients[client_id]['authenticated']:
                        self._handle_auth(client_id, data)
                    else:
                        self._handle_data(client_id, data)
                        
                except Exception as e:
                    logging.error(f"Error handling client {client_id}: {e}")
                    break
                    
        except Exception as e:
            logging.error(f"Client handler error: {e}")
        finally:
            self._cleanup_client(client_id)
    
    def _handle_auth(self, client_id, data):
        """Handle client authentication"""
        try:
            # Decrypt auth request
            decrypted = self.private_key.decrypt(
                data,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            auth_data = json.loads(decrypted)
            logging.info(f"🔐 Auth request from {client_id}: {auth_data}")
            
            # Generate session key
            session_key = secrets.token_bytes(32)
            self.clients[client_id]['session_key'] = session_key
            self.clients[client_id]['authenticated'] = True
            
            # Send auth response
            response = {
                'success': True,
                'session_key': session_key.hex(),
                'assigned_ip': self.clients[client_id]['assigned_ip'],
                'message': 'VPN tunnel established successfully!'
            }
            
            response_data = json.dumps(response).encode()
            self.clients[client_id]['socket'].send(response_data)
            self.stats['bytes_sent'] += len(response_data)
            
            logging.info(f"✅ Client {client_id} authenticated - IP: {self.clients[client_id]['assigned_ip']}")
            
        except Exception as e:
            logging.error(f"Auth error for {client_id}: {e}")
    
    def _handle_data(self, client_id, data):
        """Handle client data"""
        try:
            # Decrypt data with session key
            session_key = self.clients[client_id]['session_key']
            
            iv = data[:16]
            cipher = Cipher(algorithms.AES(session_key), modes.CBC(iv), backend=default_backend())
            decryptor = cipher.decryptor()
            decrypted = decryptor.update(data[16:]) + decryptor.finalize()
            
            # Echo back the data (simulating VPN traffic)
            message = decrypted.decode().strip()
            logging.info(f"📦 Data from {client_id}: {message}")
            
            # Send response
            response = f"VPN Server Echo: {message}"
            self._send_encrypted_data(client_id, response.encode())
            
        except Exception as e:
            logging.error(f"Data handling error for {client_id}: {e}")
    
    def _send_encrypted_data(self, client_id, data):
        """Send encrypted data to client"""
        try:
            session_key = self.clients[client_id]['session_key']
            
            # Encrypt data
            iv = secrets.token_bytes(16)
            cipher = Cipher(algorithms.AES(session_key), modes.CBC(iv), backend=default_backend())
            encryptor = cipher.encryptor()
            
            # Pad data to block size
            padding_length = 16 - (len(data) % 16)
            padded_data = data + bytes([padding_length] * padding_length)
            
            encrypted = encryptor.update(padded_data) + encryptor.finalize()
            
            # Send IV + encrypted data
            full_data = iv + encrypted
            self.clients[client_id]['socket'].send(full_data)
            self.stats['bytes_sent'] += len(full_data)
            
        except Exception as e:
            logging.error(f"Send error for {client_id}: {e}")
    
    def _cleanup_client(self, client_id):
        """Clean up client connection"""
        if client_id in self.clients:
            try:
                self.clients[client_id]['socket'].close()
            except:
                pass
            del self.clients[client_id]
            logging.info(f"🔌 Client {client_id} disconnected")
    
    def stop(self):
        """Stop the server"""
        self.running = False
        if hasattr(self, 'server_socket'):
            self.server_socket.close()
        logging.info("🛑 Server stopped")

class SimpleVPNClient:
    """Simplified VPN client for testing"""
    
    def __init__(self, host='127.0.0.1', port=5000):
        self.host = host
        self.port = port
        self.socket = None
        self.authenticated = False
        self.session_key = None
        self.assigned_ip = None
        
        # Load server public key
        with open('simple_server_public.pem', 'rb') as f:
            self.server_public_key = serialization.load_pem_public_key(
                f.read(),
                backend=default_backend()
            )
    
    def connect(self):
        """Connect to VPN server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            
            logging.info(f"🔗 Connected to VPN server at {self.host}:{self.port}")
            
            # Send auth request
            self._authenticate()
            
            if self.authenticated:
                # Test data exchange
                self._test_data_exchange()
            
        except Exception as e:
            logging.error(f"Connection error: {e}")
        finally:
            if self.socket:
                self.socket.close()
    
    def _authenticate(self):
        """Authenticate with server"""
        try:
            # Create auth request
            auth_data = {
                'client_id': secrets.token_hex(4),
                'timestamp': time.time(),
                'version': '1.0'
            }
            
            # Encrypt with server public key
            encrypted = self.server_public_key.encrypt(
                json.dumps(auth_data).encode(),
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            # Send auth request
            self.socket.send(encrypted)
            
            # Receive auth response
            response_data = self.socket.recv(4096)
            response = json.loads(response_data.decode())
            
            if response['success']:
                self.authenticated = True
                self.session_key = bytes.fromhex(response['session_key'])
                self.assigned_ip = response['assigned_ip']
                
                logging.info(f"✅ Authentication successful!")
                logging.info(f"🌐 Assigned IP: {self.assigned_ip}")
                logging.info(f"💬 Server message: {response['message']}")
            else:
                logging.error("❌ Authentication failed")
                
        except Exception as e:
            logging.error(f"Authentication error: {e}")
    
    def _test_data_exchange(self):
        """Test data exchange with server"""
        try:
            test_messages = [
                "Hello VPN Server!",
                "Testing encrypted tunnel",
                "Custom VPN is working!",
                "Performance test message"
            ]
            
            for message in test_messages:
                # Send encrypted message
                self._send_encrypted_data(message.encode())
                
                # Receive response
                response_data = self.socket.recv(4096)
                decrypted_response = self._decrypt_data(response_data)
                
                logging.info(f"📨 Sent: {message}")
                logging.info(f"📬 Received: {decrypted_response.decode()}")
                
                time.sleep(1)
                
        except Exception as e:
            logging.error(f"Data exchange error: {e}")
    
    def _send_encrypted_data(self, data):
        """Send encrypted data to server"""
        # Encrypt data with session key
        iv = secrets.token_bytes(16)
        cipher = Cipher(algorithms.AES(self.session_key), modes.CBC(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        
        # Pad data
        padding_length = 16 - (len(data) % 16)
        padded_data = data + bytes([padding_length] * padding_length)
        
        encrypted = encryptor.update(padded_data) + encryptor.finalize()
        
        # Send IV + encrypted data
        self.socket.send(iv + encrypted)
    
    def _decrypt_data(self, data):
        """Decrypt data from server"""
        iv = data[:16]
        cipher = Cipher(algorithms.AES(self.session_key), modes.CBC(iv), backend=default_backend())
        decryptor = cipher.decryptor()
        decrypted = decryptor.update(data[16:]) + decryptor.finalize()
        
        # Remove padding
        padding_length = decrypted[-1]
        return decrypted[:-padding_length]

def main():
    """Main test function"""
    print("\n🔒 SIMPLE CUSTOM VPN TEST")
    print("="*40)
    print("1. Start Server")
    print("2. Start Client")
    print("3. Exit")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        server = SimpleVPNServer()
        try:
            server.start()
        except KeyboardInterrupt:
            logging.info("Server stopped by user")
    elif choice == "2":
        client = SimpleVPNClient()
        client.connect()
    elif choice == "3":
        print("👋 Goodbye!")
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
