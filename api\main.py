from fastapi import <PERSON><PERSON><PERSON>, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from typing import List, Dict, Optional
import uvicorn
import json
import asyncio
import psutil
import time
from datetime import datetime
from secure_vpn.api.auth import router as auth_router
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware
from .security import key_middleware

app = FastAPI(title="Secure VPN API")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth_router, prefix="/api/auth", tags=["auth"])

# Integrate security middleware
app.add_middleware(HTTPSRedirectMiddleware)
app.middleware("http")(key_middleware)

# Models
class VPNStats(BaseModel):
    total_connections: int
    active_connections: int
    bytes_sent: int
    bytes_received: int
    cpu_usage: float
    memory_usage: float
    uptime: int

class Connection(BaseModel):
    id: str
    ip: str
    port: int
    connected_since: str
    bytes_sent: int
    bytes_received: int

# Store active connections
active_connections: Dict[str, Connection] = {}
stats = {
    "total_connections": 0,
    "active_connections": 0,
    "bytes_sent": 0,
    "bytes_received": 0,
    "cpu_usage": 0.0,
    "memory_usage": 0.0,
    "uptime": 0
}

# WebSocket connections
websocket_connections: List[WebSocket] = []

@app.on_event("startup")
async def startup_event():
    """Initialize the VPN dashboard"""
    # Mount static files
    app.mount("/static", StaticFiles(directory="static"), name="static")
    
    # Start background tasks
    asyncio.create_task(update_stats())

async def update_stats():
    """Update VPN statistics periodically"""
    start_time = time.time()
    while True:
        try:
            # Update system stats
            stats["cpu_usage"] = psutil.cpu_percent()
            stats["memory_usage"] = psutil.virtual_memory().percent
            stats["uptime"] = int(time.time() - start_time)
            
            # Update connection stats
            stats["active_connections"] = len(active_connections)
            
            # Broadcast updates to all connected clients
            for websocket in websocket_connections:
                try:
                    await websocket.send_json(stats)
                except:
                    websocket_connections.remove(websocket)
            
            await asyncio.sleep(1)
        except Exception as e:
            print(f"Error updating stats: {e}")
            await asyncio.sleep(1)

@app.get("/api/stats")
async def get_stats() -> VPNStats:
    """Get current VPN statistics"""
    return VPNStats(**stats)

@app.get("/api/connections")
async def get_connections() -> List[Connection]:
    """Get list of active connections"""
    return list(active_connections.values())

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await websocket.accept()
    websocket_connections.append(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # Handle incoming WebSocket messages if needed
    except WebSocketDisconnect:
        websocket_connections.remove(websocket)

@app.post("/api/connections/{connection_id}/disconnect")
async def disconnect_client(connection_id: str):
    """Disconnect a specific client"""
    if connection_id not in active_connections:
        raise HTTPException(status_code=404, detail="Connection not found")
    
    # TODO: Implement actual disconnection logic
    del active_connections[connection_id]
    return {"status": "success"}

@app.get("/api/status")
async def get_status():
    """Get overall VPN status"""
    return {
        "status": "running",
        "version": "1.0.0",
        "started_at": datetime.now().isoformat(),
        "connections": len(active_connections),
        "system": {
            "cpu": psutil.cpu_percent(),
            "memory": psutil.virtual_memory().percent,
            "disk": psutil.disk_usage('/').percent
        }
    }

@app.get("/")
async def root():
    return {"message": "Welcome to Secure VPN API"}

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True) 