#!/usr/bin/env python3
"""
Test script for the enhanced custom VPN implementation
"""

import time
import subprocess
import threading
import logging
import sys
import os
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_server():
    """Test the VPN server"""
    try:
        logging.info("🚀 Starting Custom VPN Server...")
        
        # Import and start server
        from custom_vpn_server import VPNServer
        
        server = VPNServer(host='127.0.0.1', port=5000)
        server.start()
        
    except KeyboardInterrupt:
        logging.info("Server stopped by user")
    except Exception as e:
        logging.error(f"Server error: {e}")
        import traceback
        traceback.print_exc()

def test_client():
    """Test the VPN client"""
    try:
        # Wait for server to start
        time.sleep(2)
        
        logging.info("🔗 Starting Custom VPN Client...")
        
        # Import and start client
        from custom_vpn_client import VPNClient
        
        client = VPNClient('127.0.0.1', 5000)
        client.connect()
        
    except KeyboardInterrupt:
        logging.info("Client stopped by user")
    except Exception as e:
        logging.error(f"Client error: {e}")
        import traceback
        traceback.print_exc()

def check_requirements():
    """Check if required dependencies are available"""
    try:
        import cryptography
        logging.info("✅ Cryptography library available")
    except ImportError:
        logging.error("❌ Cryptography library not found. Install with: pip install cryptography")
        return False
    
    # Check platform-specific requirements
    if sys.platform.startswith("linux"):
        if not os.path.exists("/dev/net/tun"):
            logging.error("❌ TUN/TAP not available. Enable with: sudo modprobe tun")
            return False
        logging.info("✅ Linux TUN/TAP available")
    elif sys.platform == "win32":
        logging.info("ℹ️  Windows TAP driver may be required for full functionality")
    
    return True

def show_vpn_status():
    """Show VPN connection status"""
    print("\n" + "="*60)
    print("🔒 CUSTOM VPN STATUS")
    print("="*60)
    
    # Check if server is running
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', 5000))
        sock.close()
        
        if result == 0:
            print("🟢 VPN Server: RUNNING on 127.0.0.1:5000")
        else:
            print("🔴 VPN Server: NOT RUNNING")
    except:
        print("🔴 VPN Server: NOT RUNNING")
    
    # Check for log files
    if os.path.exists('vpn_server.log'):
        print("📋 Server Log: vpn_server.log")
    if os.path.exists('vpn_client.log'):
        print("📋 Client Log: vpn_client.log")
    
    # Check for keys
    if os.path.exists('keys/server_private.pem'):
        print("🔑 Server Keys: GENERATED")
    else:
        print("🔑 Server Keys: NOT FOUND")
    
    print("="*60)

def main():
    """Main test function"""
    print("\n🔒 CUSTOM VPN TESTING SUITE")
    print("="*50)
    
    if not check_requirements():
        print("❌ Requirements check failed")
        return
    
    print("\nSelect test mode:")
    print("1. Start VPN Server")
    print("2. Start VPN Client")
    print("3. Show VPN Status")
    print("4. Run Full Test (Server + Client)")
    print("5. Exit")
    
    try:
        choice = input("\nEnter choice (1-5): ").strip()
        
        if choice == "1":
            test_server()
        elif choice == "2":
            test_client()
        elif choice == "3":
            show_vpn_status()
        elif choice == "4":
            print("\n🚀 Starting Full VPN Test...")
            
            # Start server in background thread
            server_thread = threading.Thread(target=test_server, daemon=True)
            server_thread.start()
            
            # Start client
            test_client()
        elif choice == "5":
            print("👋 Goodbye!")
        else:
            print("❌ Invalid choice")
            
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        logging.error(f"Test error: {e}")

if __name__ == "__main__":
    main()
